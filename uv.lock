version = 1
revision = 1
requires-python = ">=3.13"

[[package]]
name = "asttokens"
version = "3.0.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/4a/e7/82da0a03e7ba5141f05cce0d302e6eed121ae055e0456ca228bf693984bc/asttokens-3.0.0.tar.gz", hash = "sha256:0dcd8baa8d62b0c1d118b399b2ddba3c4aff271d0d7a9e0d4c1681c79035bbc7", size = 61978 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/25/8a/c46dcc25341b5bce5472c718902eb3d38600a903b14fa6aeecef3f21a46f/asttokens-3.0.0-py3-none-any.whl", hash = "sha256:e3078351a059199dd5138cb1c706e6430c05eff2ff136af5eb4790f9d28932e2", size = 26918 },
]

[[package]]
name = "bitarray"
version = "3.3.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/93/34/ce4e18fb096083b9e1b9d0ac5750aecc8ba2e8047b3096b9ee1c52b72ae6/bitarray-3.3.0.tar.gz", hash = "sha256:044909ce63b77adf5739de40e2f621473453e9d4bdb913ef2d14c185a4532ce7", size = 138729 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/a7/25/8424f76d9bcc8cb94faf3037cff874ad04e5bce5555cd974371a0a3e3f08/bitarray-3.3.0-cp313-cp313-macosx_10_13_x86_64.whl", hash = "sha256:d6fa29c8b47eaee2efbc705825f9ec6aa6540e926639776ed83116f7b4678c0e", size = 136925 },
    { url = "https://files.pythonhosted.org/packages/47/6e/7192989bb0e815bd285c71f7779df048241859db9be3e8e05d83e25e72dc/bitarray-3.3.0-cp313-cp313-macosx_11_0_arm64.whl", hash = "sha256:daba653f79735efbe8a8e1e921dc0b4c8bd4fa3b8d01f4b7d3fc247dd26d7878", size = 133899 },
    { url = "https://files.pythonhosted.org/packages/fc/85/e963a4e5107d2aa4f3375d9ae1e00cebe7ab9eef74fb0d3e60091dff7658/bitarray-3.3.0-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl", hash = "sha256:b5ae6d3e15ed28be993b0556dc3ab04c4363c7f647bcfb76bfbf18cdd148c285", size = 307958 },
    { url = "https://files.pythonhosted.org/packages/dc/e7/791dd45f7b4985b9ecf7268e66cf72d0b913ee49a70f74fa87bc735b11d9/bitarray-3.3.0-cp313-cp313-manylinux_2_17_ppc64le.manylinux2014_ppc64le.whl", hash = "sha256:1bd6ba5ea4a9eb8ff5dc413c78c85894aec26c96a976d710b5cf01779ba9c8a8", size = 322047 },
    { url = "https://files.pythonhosted.org/packages/c4/88/5db075567ef8452ca78848d092215d1e3c7419b2d8a39b790d3d9380d62b/bitarray-3.3.0-cp313-cp313-manylinux_2_17_s390x.manylinux2014_s390x.whl", hash = "sha256:083c8205d980b4288826fff62e7bc5cfcc401c719c93c7cc450b031bd3f8bcef", size = 323903 },
    { url = "https://files.pythonhosted.org/packages/69/83/9a628470f6baf8e9adbd558fc886eaffb05e295ae65ab6c9045ea759aa89/bitarray-3.3.0-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl", hash = "sha256:77afea3cb010f4b9e97da0a666b6a825540e06878476324f2c7e3532f0ebd56d", size = 308268 },
    { url = "https://files.pythonhosted.org/packages/c7/10/86920751ee3a1d8c59cb8a4700621ea2678c8084d9b5de47ef140dac434f/bitarray-3.3.0-cp313-cp313-manylinux_2_5_i686.manylinux1_i686.manylinux_2_17_i686.manylinux2014_i686.whl", hash = "sha256:7e1d12cbd132df174aaa50fbcb2b790915ce77a1693d777a7b768d7d64b9180f", size = 296521 },
    { url = "https://files.pythonhosted.org/packages/47/89/1dd5ab063252754ec8735bb324b1777b56618e13c02a58cbfe1f29c0fd7c/bitarray-3.3.0-cp313-cp313-musllinux_1_2_aarch64.whl", hash = "sha256:26ca7a3fba83298865384bd0551e1d81eed96fe2f9077395c43d390a10aadc30", size = 301691 },
    { url = "https://files.pythonhosted.org/packages/ea/a9/74ce706d1205e2dd6d692447c6d6aa1fa67853be91c82399e36b6e64fd10/bitarray-3.3.0-cp313-cp313-musllinux_1_2_i686.whl", hash = "sha256:25f7a5781d51bd22b0c2412dabc9f2e5fd24e0503900fd8aeebb9c2549442d74", size = 293208 },
    { url = "https://files.pythonhosted.org/packages/29/c5/3a256be30ae175ba67f76e7d35b0a703a6cc6f1cd969eb480a864e759d28/bitarray-3.3.0-cp313-cp313-musllinux_1_2_ppc64le.whl", hash = "sha256:f81b6c8420d1f8221953031b26a808dcb23220b5a83b2ac5d51f607b7e4fd45c", size = 316886 },
    { url = "https://files.pythonhosted.org/packages/6d/00/7c28cd07f38f668d2a4ca079fff1963acefe5871a45ec404f719bcd99590/bitarray-3.3.0-cp313-cp313-musllinux_1_2_s390x.whl", hash = "sha256:d672de25e5eba99cd5d8a606860b7a3dc958c3b00a719ecd90be8a63ac12d32d", size = 329129 },
    { url = "https://files.pythonhosted.org/packages/0b/62/7c4aaafcee6ce0de33d0d827de66418e28b78077ee703ebc080e66b90b0d/bitarray-3.3.0-cp313-cp313-musllinux_1_2_x86_64.whl", hash = "sha256:dc60f283ea4b450985b56c821ec798a3e1107730f73802f71657be8ead63c148", size = 300665 },
    { url = "https://files.pythonhosted.org/packages/98/ad/d3bbe4233b875ec489b22981f36af3d49f688e1ee748a9535147ecbcb2b9/bitarray-3.3.0-cp313-cp313-win32.whl", hash = "sha256:88bceb0af0c9ce1a6ed30fe57c8ad5d86bca431bba97af81b5a032fd8dd87807", size = 130366 },
    { url = "https://files.pythonhosted.org/packages/4c/b2/f41f26cb7b42c6f13f2fed85287845f8fc8fddbb56157cc730c9a1e0bdd3/bitarray-3.3.0-cp313-cp313-win_amd64.whl", hash = "sha256:5a72c3c1b0095213b13bdb478c9de0d85588d19a314252c0f6f0ced0e992abd9", size = 137251 },
]

[[package]]
name = "colorama"
version = "0.4.6"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/d8/53/6f443c9a4a8358a93a6792e2acffb9d9d5cb0a5cfd8802644b7b1c9a02e4/colorama-0.4.6.tar.gz", hash = "sha256:08695f5cb7ed6e0531a20572697297273c47b8cae5a63ffc6d6ed5c201be6e44", size = 27697 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/d1/d6/3965ed04c63042e047cb6a3e6ed1a63a35087b6a609aa3a15ed8ac56c221/colorama-0.4.6-py2.py3-none-any.whl", hash = "sha256:4f1d9991f5acc0ca119f9d443620b77f9d6b33703e51011c16baf57afb285fc6", size = 25335 },
]

[[package]]
name = "executing"
version = "2.2.0"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/91/50/a9d80c47ff289c611ff12e63f7c5d13942c65d68125160cefd768c73e6e4/executing-2.2.0.tar.gz", hash = "sha256:5d108c028108fe2551d1a7b2e8b713341e2cb4fc0aa7dcf966fa4327a5226755", size = 978693 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/7b/8f/c4d9bafc34ad7ad5d8dc16dd1347ee0e507a52c3adb6bfa8887e1c6a26ba/executing-2.2.0-py2.py3-none-any.whl", hash = "sha256:11387150cad388d62750327a53d3339fad4888b39a6fe233c3afbb54ecffd3aa", size = 26702 },
]

[[package]]
name = "icecream"
version = "2.1.4"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "asttokens" },
    { name = "colorama" },
    { name = "executing" },
    { name = "pygments" },
]
sdist = { url = "https://files.pythonhosted.org/packages/78/5e/9f41831f032b9ce456c919c4989952562fcc2b0eb8c038080c24ce20d6cd/icecream-2.1.4.tar.gz", hash = "sha256:58755e58397d5350a76f25976dee7b607f5febb3c6e1cddfe6b1951896e91573", size = 15872 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/57/1d/43ef7a6875190e6745ffcd1b12c7aaa7efed082897401e311ee1cd75c8b2/icecream-2.1.4-py3-none-any.whl", hash = "sha256:7bb715f69102cae871b3a361c3b656536db02cfcadac9664c673581cac4df4fd", size = 14782 },
]

[[package]]
name = "intel-4004-emulator"
version = "0.1.0"
source = { virtual = "." }
dependencies = [
    { name = "bitarray" },
    { name = "icecream" },
    { name = "rich" },
    { name = "typeguard" },
]

[package.metadata]
requires-dist = [
    { name = "bitarray", specifier = ">=3.3.0" },
    { name = "icecream", specifier = ">=2.1.4" },
    { name = "rich", specifier = ">=14.0.0" },
    { name = "typeguard", specifier = ">=4.4.2" },
]

[[package]]
name = "markdown-it-py"
version = "3.0.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "mdurl" },
]
sdist = { url = "https://files.pythonhosted.org/packages/38/71/3b932df36c1a044d397a1f92d1cf91ee0a503d91e470cbd670aa66b07ed0/markdown-it-py-3.0.0.tar.gz", hash = "sha256:e3f60a94fa066dc52ec76661e37c851cb232d92f9886b15cb560aaada2df8feb", size = 74596 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/42/d7/1ec15b46af6af88f19b8e5ffea08fa375d433c998b8a7639e76935c14f1f/markdown_it_py-3.0.0-py3-none-any.whl", hash = "sha256:355216845c60bd96232cd8d8c40e8f9765cc86f46880e43a8fd22dc1a1a8cab1", size = 87528 },
]

[[package]]
name = "mdurl"
version = "0.1.2"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/d6/54/cfe61301667036ec958cb99bd3efefba235e65cdeb9c84d24a8293ba1d90/mdurl-0.1.2.tar.gz", hash = "sha256:bb413d29f5eea38f31dd4754dd7377d4465116fb207585f97bf925588687c1ba", size = 8729 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/b3/38/89ba8ad64ae25be8de66a6d463314cf1eb366222074cfda9ee839c56a4b4/mdurl-0.1.2-py3-none-any.whl", hash = "sha256:84008a41e51615a49fc9966191ff91509e3c40b939176e643fd50a5c2196b8f8", size = 9979 },
]

[[package]]
name = "pygments"
version = "2.19.1"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/7c/2d/c3338d48ea6cc0feb8446d8e6937e1408088a72a39937982cc6111d17f84/pygments-2.19.1.tar.gz", hash = "sha256:61c16d2a8576dc0649d9f39e089b5f02bcd27fba10d8fb4dcc28173f7a45151f", size = 4968581 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/8a/0b/9fcc47d19c48b59121088dd6da2488a49d5f72dacf8262e2790a1d2c7d15/pygments-2.19.1-py3-none-any.whl", hash = "sha256:9ea1544ad55cecf4b8242fab6dd35a93bbce657034b0611ee383099054ab6d8c", size = 1225293 },
]

[[package]]
name = "rich"
version = "14.0.0"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "markdown-it-py" },
    { name = "pygments" },
]
sdist = { url = "https://files.pythonhosted.org/packages/a1/53/830aa4c3066a8ab0ae9a9955976fb770fe9c6102117c8ec4ab3ea62d89e8/rich-14.0.0.tar.gz", hash = "sha256:82f1bc23a6a21ebca4ae0c45af9bdbc492ed20231dcb63f297d6d1021a9d5725", size = 224078 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/0d/9b/63f4c7ebc259242c89b3acafdb37b41d1185c07ff0011164674e9076b491/rich-14.0.0-py3-none-any.whl", hash = "sha256:1c9491e1951aac09caffd42f448ee3d04e58923ffe14993f6e83068dc395d7e0", size = 243229 },
]

[[package]]
name = "typeguard"
version = "4.4.2"
source = { registry = "https://pypi.org/simple" }
dependencies = [
    { name = "typing-extensions" },
]
sdist = { url = "https://files.pythonhosted.org/packages/70/60/8cd6a3d78d00ceeb2193c02b7ed08f063d5341ccdfb24df88e61f383048e/typeguard-4.4.2.tar.gz", hash = "sha256:a6f1065813e32ef365bc3b3f503af8a96f9dd4e0033a02c28c4a4983de8c6c49", size = 75746 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/cf/4b/9a77dc721aa0b7f74440a42e4ef6f9a4fae7324e17f64f88b96f4c25cc05/typeguard-4.4.2-py3-none-any.whl", hash = "sha256:77a78f11f09777aeae7fa08585f33b5f4ef0e7335af40005b0c422ed398ff48c", size = 35801 },
]

[[package]]
name = "typing-extensions"
version = "4.13.1"
source = { registry = "https://pypi.org/simple" }
sdist = { url = "https://files.pythonhosted.org/packages/76/ad/cd3e3465232ec2416ae9b983f27b9e94dc8171d56ac99b345319a9475967/typing_extensions-4.13.1.tar.gz", hash = "sha256:98795af00fb9640edec5b8e31fc647597b4691f099ad75f469a2616be1a76dff", size = 106633 }
wheels = [
    { url = "https://files.pythonhosted.org/packages/df/c5/e7a0b0f5ed69f94c8ab7379c599e6036886bffcde609969a5325f47f1332/typing_extensions-4.13.1-py3-none-any.whl", hash = "sha256:4b6cf02909eb5495cfbc3f6e8fd49217e6cc7944e145cdda8caa3734777f9e69", size = 45739 },
]
