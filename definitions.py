from enum import IntEnum, StrEnum, auto, Enum


class Instruction(Enum):
    NOP = auto()
    JCN = auto()
    FIM = auto()
    SRC = auto()
    FIN = auto()
    JIN = auto()
    JUN = auto()
    JMS = auto()
    INC = auto()
    ISZ = auto()
    ADD = auto()
    SUB = auto()
    LD = auto()
    XCH = auto()
    BBL = auto()
    LDM = auto()
    WRM = auto()
    WMP = auto()
    WRR = auto()
    WPM = auto()
    WR0 = auto()
    WR1 = auto()
    WR2 = auto()
    WR3 = auto()
    SBM = auto()
    RDM = auto()
    RDR = auto()
    ADM = auto()
    RD0 = auto()
    RD1 = auto()
    RD2 = auto()
    RD3 = auto()
    CLB = auto()
    CLC = auto()
    IAC = auto()
    CMC = auto()
    CMA = auto()
    RAL = auto()
    RAR = auto()
    TCC = auto()
    DAC = auto()
    TCS = auto()
    STC = auto()
    DAA = auto()
    KBP = auto()
    DCL = auto()


class InstructionValue(IntEnum):
    NOP = 0
    JCN = 16
    FIM = 32
    SRC = 33
    FIN = 48
    JIN = 49
    JUN = 64
    JMS = 80
    INC = 96
    ISZ = 112
    ADD = 128
    SUB = 144
    LD = 160
    XCH = 176
    BBL = 192
    LDM = 208
    WRM = 224
    WMP = 225
    WRR = 226
    WPM = 227
    WR0 = 228
    WR1 = 229
    WR2 = 230
    WR3 = 231
    SBM = 232
    RDM = 233
    RDR = 234
    ADM = 235
    RD0 = 236
    RD1 = 237
    RD2 = 238
    RD3 = 239
    CLB = 240
    CLC = 241
    IAC = 242
    CMC = 243
    CMA = 244
    RAL = 245
    RAR = 246
    TCC = 247
    DAC = 248
    TCS = 249
    STC = 250
    DAA = 251
    KBP = 252
    DCL = 253


class Flag(IntEnum):
    CLEAR = 0
    SET = 1

    def __str__(self) -> str:
        return self.name


class InstructionPhase(IntEnum):
    FETCH_NEXT = 0
    CONTINUE = 1

    def __str__(self) -> str:
        return self.name


class MemoryData(StrEnum):
    RAM_CHAR = auto()
    RAM_STATUS = auto()
    RAM_OUTPUT = auto()
    ROM_ADDR = auto()
    ROM_IO = auto()
