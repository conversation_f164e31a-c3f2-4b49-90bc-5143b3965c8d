from textual.app import App, ComposeR<PERSON>ult
from textual.widgets import <PERSON><PERSON>, Footer, Select, Static, Label, Checkbox
from textual.containers import Horizontal
from textual.containers import Container
from textual import on
from rich_text import (
    header_table,
    create_ram_table,
    create_register_table,
    create_stack_table,
    flags_state,
)


class RAMViewer(Static):
    """Widget that displays RAM data with bank and chip selection controls."""

    # def __init__(self, **kwargs):
    #     super().__init__(**kwargs)
    selected_bank = 0
    selected_chip = 0

    def compose(self):
        """Compose the widget layout."""
        yield Horizontal(
            Select(
                [(str(bank_id), str(bank_id)) for bank_id in range(4)],
                id="bank_selector",
                prompt="Bank",
            ),
            Select(
                [(str(chip_id), str(chip_id)) for chip_id in range(4)],
                id="chip_selector",
                prompt="Chip",
            ),
        )
        yield Static(header_table, id="table_headers")
        yield Static(id="ram_data_display")

    def on_mount(self):
        """Initialize the RAM display when the widget is mounted."""
        self.update_ram_display()

    def on_select_changed(self, event: Select.Changed):
        """Handle bank or chip selection changes."""
        if event.value == Select.BLANK:
            return
        if event.select.id == "bank_selector":
            self.selected_bank = int(event.value)
        elif event.select.id == "chip_selector":
            self.selected_chip = int(event.value)
        self.update_ram_display()

    def update_ram_display(self):
        """Update the RAM data display with the currently selected bank and chip."""
        ram_display_widget = self.query_one("#ram_data_display", Static)
        updated_table = create_ram_table(self.selected_bank, self.selected_chip)
        ram_display_widget.update(updated_table)


class RegViewer(Static):
    """Widget that displays the stack data."""

    def compose(self):
        """Compose the widget layout."""
        yield Label("Registers", id="reglabel")
        yield Static(create_register_table(), id="register_display")


class StackViewer(Static):
    """Widget that displays the stack data."""

    def compose(self):
        """Compose the widget layout."""
        yield Label("Stack", id="stacklabel")
        yield Static(create_stack_table(), id="stack_display")


class StateViewer(Static):
    """Widget that displays the state data."""

    carry, test = flags_state()

    def compose(self):
        """Compose the widget layout."""
        yield Checkbox(
            "Carry", value=self.carry, id="carry", button_first=False, compact=True
        )
        yield Checkbox(
            "Test", value=self.test, id="test", button_first=False, compact=True
        )


class CPUViewer(Static):
    """Widget that displays CPU data."""

    def compose(self):
        """Compose the widget layout."""
        yield RegViewer(id="regviewer")
        yield StackViewer(id="stackviewer")
        yield StateViewer(id="stateviewer")


class ViewerApp(App):
    """Main application for viewing RAM memory bank data."""

    BINDINGS = [
        ("d", "toggle_dark", "Toggle Dark Mode"),
        ("q", "quit", "Quit"),
    ]
    CSS_PATH = "textual_ui.css"

    def compose(self) -> ComposeResult:
        """Compose the main application layout."""
        yield Header()
        yield Footer()
        yield Container(
            Select(
                [("CPU", "CPU"), ("RAM", "RAM")],
                id="view_selector",
                value="CPU",
                prompt="View",
            ),
            RAMViewer(id="ram_viewer"),
            CPUViewer(id="cpu_viewer"),
        )
        # yield Select(
        #     [("CPU", "CPU"), ("RAM", "RAM")],
        #     id="view_selector",
        #     value="CPU",
        #     prompt="View",
        # )
        # yield RAMViewer(id="ram_viewer")
        # yield CPUViewer(id="cpu_viewer")

    @on(Select.Changed, "#view_selector")
    def switch_view(self, event: Select.Changed):
        """Switch between CPU and RAM views."""
        if event.value == "CPU":
            try:
                self.remove_class("show_ram")
            except Exception as e:
                self.log(f"Error removing show_ram class: {e}")
            self.add_class("show_cpu")

        elif event.value == "RAM":
            try:
                self.remove_class("show_cpu")
            except Exception as e:
                self.log(f"Error removing show_cpu class: {e}")
            self.add_class("show_ram")


if __name__ == "__main__":
    ram_viewer_app = ViewerApp()
    ram_viewer_app.run()
