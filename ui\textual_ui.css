Screen {
    align: center middle;
}

Container {
    height: 90%;
    width: 90%;
    border: round white;
}

RAMViewer {
    layout: vertical;
    margin: 1;
}

CPUViewer {
    layout: horizontal;
    margin: 4;

}

RegViewer {
    width: 20;
    height: 14;
    margin: 1;
    dock: right
}

/* #reglabel {
    offset: 6 0;

} */

#bank_selector {
    width: 12;
}

#chip_selector {
    width: 12;
}

#ram_data_display {
    height: 13;
}

#view_selector {
    dock: right;
    width: 15;
    margin: 2;
}

#cpu_viewer {
    display: none;
}

#ram_viewer {
    display: none;
}

.show_cpu #cpu_viewer {
    display: block;
}

.show_ram #ram_viewer {
    display: block;
}