Screen {
    align: center middle;
}

Container {
    height: 90%;
    width: 90%;
    border: round white;
}

RAMViewer {
    layout: vertical;
    margin: 1;
}

CPUViewer {
    layout: horizontal;
    margin: 4;

}

#regviewer {
    layout: vertical;
    width: 20;
    height: 18;
    margin: 0 5;
    dock: right;
}

#stackviewer {
    layout: vertical;
    width: 25;
    height: 14;
    margin: 1;
    /* dock: right; */
}

#stateviewer {
    layout: horizontal;
    offset: 5 2;
    margin: 1;
    /* dock: right; */
}

#carry {
    margin: 1;
}

#test {
    margin: 1;
}





#reglabel {
    offset: 6 0;

}

#bank_selector {
    width: 12;
}

#chip_selector {
    width: 12;
}

#ram_data_display {
    height: 8;
}

#view_selector {
    dock: right;
    width: 15;
    padding: 0 2;
}

#cpu_viewer {
    display: none;
}

#ram_viewer {
    display: none;
}

.show_cpu #cpu_viewer {
    display: block;
}

.show_ram #ram_viewer {
    display: block;
}