from argparse import ArgumentParser
from processor import CPU
from definitions import MemoryData
from config import mem

parser = ArgumentParser()

parser.add_argument(
    "-f",
    "--file",
    help="file to read",
    type=str,
    default="programs/hex_code.txt",
)

args = parser.parse_args()


mem.ROM[0].read_from_file(args.file)
cpu = CPU()
c = -1
while True:
    input("Hit enter to continue")
    rom_page = cpu.program_counter[0].PAGE.value
    rom_addr = cpu.program_counter[0].get_address()
    rom_data = mem.read(MemoryData.ROM_ADDR, bank=rom_page, byte=rom_addr)
    cpu.process_instruction(rom_data)
    print(cpu)
    c += 1
    print(f"{c=}")
    mem.RAM[0][0].print_all()
    # breakpoint()
    print()
    print()
    print()
    break
