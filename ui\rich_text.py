from rich.console import Console
from rich.table import Table
from rich.box import ASCII
import sys
import os

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from config import mem
from main import cpu


# Console for header table rendering
header_console = Console()

# Header table that shows column labels for the RAM data
header_table = Table(
    show_header=True,
    header_style="magenta",
    show_edge=False,
    show_lines=False,
    box=ASCII,
)
header_table.add_column(header="REG", width=3, overflow="crop")
header_table.add_column(max_width=1)
header_table.add_column("CHAR", width=77, justify="center")
header_table.add_column(max_width=1)
header_table.add_column("STATUS", width=17, justify="center")

# Console for RAM data table rendering
# ram_console = Console()
# ram_console.print(header_table)

# Template table configuration (not used directly, just for reference)
_ram_table_template = Table(
    show_header=True,
    box=ASCII,
    header_style="blue",
    show_edge=False,
    row_styles=["none", "dim"],
)


def format_ram_data_for_table(ram_chip):
    """Convert RAM chip data into table rows format.

    Args:
        ram_chip: RAM object containing data and status arrays

    Returns:
        List of table rows, each row containing formatted data and status values
    """
    table_rows = []
    for row_index, data_row in enumerate(ram_chip.chip):
        formatted_row = []
        # Add row number
        formatted_row.append(f"{row_index}")
        # Add separator
        formatted_row.append("")
        # Add 16 data columns (0-F)
        for column_index in range(16):
            formatted_row.append(str(data_row[column_index]))
        # Add separator
        formatted_row.append("")
        # Add 4 status columns (0-3)
        for status_index in range(4):
            formatted_row.append(str(ram_chip.chip[row_index].status[status_index]))
        table_rows.append(formatted_row)
    return table_rows


def create_ram_table(bank_index, chip_index):
    """Create a Rich table displaying RAM data for the specified bank and chip.

    Args:
        bank_index: Index of the memory bank (0-3)
        chip_index: Index of the chip within the bank (0-3)

    Returns:
        Rich Table object containing the formatted RAM data
    """
    # Create a new table instance each time
    ram_data_table = Table(
        show_header=True,
        box=ASCII,
        header_style="blue",
        show_edge=False,
        row_styles=["none", "dim"],
    )

    # Add columns to the table
    # Row number column
    ram_data_table.add_column("", width=3, justify="center")
    # Separator column
    ram_data_table.add_column("")
    # Data columns (0-F, 16 columns total)
    for hex_digit in "0123456789ABCDEF":
        ram_data_table.add_column(hex_digit, width=2)
    # Separator column
    ram_data_table.add_column("")
    # Status columns (0-3, 4 columns total)
    for status_col in range(4):
        ram_data_table.add_column(str(status_col), width=2)

    # Get data and add rows
    selected_ram_chip = mem.RAM[bank_index][chip_index]
    formatted_rows = format_ram_data_for_table(selected_ram_chip)
    for row_data in formatted_rows:
        ram_data_table.add_row(*row_data)

    return ram_data_table


# def print_ram_table(bank_index, chip_index):
#     """Print the RAM table for the specified bank and chip.

#     Args:
#         bank_index: Index of the memory bank (0-3)
#         chip_index: Index of the chip within the bank (0-3)
#     """
#     ram_table = create_ram_table(bank_index, chip_index)
#     ram_console.print(ram_table)


# print_ram_table(0, 0)


def format_register_data_for_table():
    """Convert register data into table rows format.

    Args:
        register: Register object containing data

    Returns:
        List of table rows, each row containing formatted data
    """
    table_rows = []
    for idx in range(0, 16, 2):
        formatted_row = []
        # Add row number
        formatted_row.append(f" R{idx}")
        # Add separator
        formatted_row.append(
            f"{cpu.index_registers[idx]}  {cpu.index_registers[idx + 1]}"
        )
        formatted_row.append(f"R{idx + 1}")

        table_rows.append(formatted_row)
    return table_rows


def create_register_table():
    """Create a Rich table displaying register data.

    Args:
        register: Register object containing data

    Returns:
        Rich Table object containing the formatted register data
    """
    register_table = Table(
        show_header=False,
        box=ASCII,
        header_style="blue",
        show_edge=False,
    )

    formatted_rows = format_register_data_for_table()
    for row_data in formatted_rows:
        register_table.add_row(*row_data)
    register_table.columns[0].justify = "right"
    register_table.columns[0].style = "purple"
    register_table.columns[2].style = "purple"

    return register_table
